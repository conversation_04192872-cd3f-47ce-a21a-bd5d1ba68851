name: Create and publish Docker image for extproc

on:
  workflow_dispatch:
  push:
    branches: [ "main" ]

jobs:
  build_and_push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Check out the repo
      uses: actions/checkout@v4

    - name: Log in to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.extproc
        push: ${{ github.event_name != 'pull_request' }} # Only push on merge to main, not on PRs
        tags: |
          ghcr.io/${{ github.repository_owner }}/llm_semantic_router/extproc:${{ github.sha }}
          ghcr.io/${{ github.repository_owner }}/llm_semantic_router/extproc:latest 