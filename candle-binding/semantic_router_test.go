package candle_binding

import (
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"os"
	"runtime"
	"strings"
	"sync"
	"testing"
	"time"
)

// ResetModel completely resets the model in Rust side to allow loading a new model
func ResetModel() {
	// Clean up the model state
	modelInitialized = false
	runtime.GC()
	SetMemoryCleanupHandler()
	// Create a new sync.Once to allow reinitialization
	initOnce = sync.Once{}
	time.Sleep(200 * time.Millisecond)
}

// Test models to benchmark
var testModels = []struct {
	name    string
	modelID string
	size    string // Model size category for comparison
}{
	// per https://huggingface.co/models?pipeline_tag=sentence-similarity&sort=downloads
	// TF models like bert-base-uncased are not supported by candle yet, need to convert to pytorch through https://github.com/huggingface/transformers/blob/main/src/transformers/models/bert/convert_bert_original_tf_checkpoint_to_pytorch.py
	{
		name:    "Paraphrase-multilingual-MiniLM-L12-v2",
		modelID: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
		size:    "small",
	},
	{
		name:    "MiniLM-L12",
		modelID: "sentence-transformers/all-MiniLM-L12-v2",
		size:    "medium",
	},
	{
		name:    "BGE-large",
		modelID: "BAAI/bge-large-zh-v1.5",
		size:    "large",
	},
}

// More comprehensive test dataset for better model differentiation
var testDataset = []struct {
	text1 string
	text2 string
	class string // Semantic class for evaluation
}{
	{
		text1: "I love machine learning",
		text2: "I enjoy artificial intelligence",
		class: "SIMILAR",
	},
	{
		text1: "I need to buy groceries",
		text2: "The stock market is volatile",
		class: "DIFFERENT",
	},
	{
		text1: "Python is a programming language",
		text2: "Python is a type of snake",
		class: "AMBIGUOUS",
	},
	{
		text1: "This is a test sentence",
		text2: "This is a test sentence",
		class: "IDENTICAL",
	},
}

// Initialize the model before running tests
func initBERTModel(t *testing.T, modelID string, useCPU bool) bool {
	// Print current working directory and environment
	pwd, _ := os.Getwd()
	fmt.Printf("Current working directory: %s\n", pwd)
	fmt.Printf("HF_HOME: %s\n", os.Getenv("HF_HOME"))

	// Use the model ID directly for Hugging Face download
	fmt.Printf("Initializing BERT model with ID: %s (CPU: %v)\n", modelID, useCPU)

	// Force cleanup of any previous model
	ResetModel()

	// Initialize the model
	err := InitModel(modelID, useCPU)
	if err != nil {
		t.Logf("Failed to initialize BERT model: %v", err)
		return false
	}

	// Verify model is initialized
	if !IsModelInitialized() {
		t.Logf("BERT model initialization failed")
		return false
	}

	// Do a quick test to verify it works
	score := CalculateSimilarityDefault("test", "test")
	if score < 0 {
		t.Logf("Model initialization succeeded but similarity calculation failed")
		return false
	}

	fmt.Printf("Model successfully initialized\n")
	return true
}

// TestTokenizeNorvigText tests downloading and tokenizing text from norvig.com/big.txt
func TestTokenizeNorvigText(t *testing.T) {
	// Initialize a small model for tokenization
	modelID := "sentence-transformers/all-MiniLM-L6-v2"
	if !initBERTModel(t, modelID, true) { // Use CPU for simplicity
		t.Fatal("Failed to initialize model for tokenization test")
	}

	// Download content from norvig.com
	norvigURL := "https://norvig.com/big.txt"
	resp, err := http.Get(norvigURL)
	if err != nil {
		t.Fatalf("Failed to download text content: %v", err)
	}
	defer resp.Body.Close()

	content, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("Failed to read text content: %v", err)
	}

	// Get text content
	textContent := string(content)

	// Tokenize the content
	tokenResult, err := TokenizeTextDefault(textContent)
	if err != nil {
		t.Fatalf("Failed to tokenize Norvig text content: %v", err)
	}

	// Print statistics
	t.Logf("Successfully tokenized text from norvig.com/big.txt")
	t.Logf("Total tokens: %d", len(tokenResult.TokenIDs))

	// Try with a smaller max_length to test the parameter
	tokenResultSmaller, err := TokenizeText(textContent[:1000], 512)
	if err != nil {
		t.Fatalf("Failed to tokenize with smaller max_length: %v", err)
	}
	t.Logf("Tokenized with max_length=128, tokens: %d", len(tokenResultSmaller.TokenIDs))

	// Write tokens to a file
	outputFile := "/tmp/norvig_big_tokens.txt"
	var output strings.Builder

	output.WriteString(fmt.Sprintf("Tokenization of Norvig big.txt\n"))
	output.WriteString(fmt.Sprintf("Total tokens: %d\n\n", len(tokenResult.TokenIDs)))
	output.WriteString(fmt.Sprintf("TOKEN_ID\tTOKEN\n"))

	for i, token := range tokenResult.Tokens {
		output.WriteString(fmt.Sprintf("%d\t%s\n", tokenResult.TokenIDs[i], token))
	}

	err = ioutil.WriteFile(outputFile, []byte(output.String()), 0644)
	if err != nil {
		t.Fatalf("Failed to write tokens to file: %v", err)
	}

	t.Logf("Tokens written to file: %s", outputFile)
}

// TestModelBenchmarking tests different models for accuracy and performance
func TestModelBenchmarking(t *testing.T) {
	// Check if GPU is available first
	gpuAvailable := false
	if os.Getenv("FORCE_CPU_ONLY") != "1" {
		t.Log("Checking GPU availability...")
		gpuAvailable = checkGPUAvailability(t)
		t.Logf("GPU available: %v", gpuAvailable)
	}

	// Define device types to test
	devices := []struct {
		name   string
		useCPU bool
	}{
		{name: "CPU", useCPU: true},
	}

	// Only add GPU if it's available
	if gpuAvailable {
		devices = append(devices, struct {
			name   string
			useCPU bool
		}{name: "GPU", useCPU: false})
	}

	results := make(map[string]map[string]map[string]float64) // model -> device -> metric -> value

	// Initialize results map
	for _, model := range testModels {
		results[model.name] = make(map[string]map[string]float64)
		for _, device := range devices {
			results[model.name][device.name] = make(map[string]float64)
		}
	}

	// Run benchmarks for each model and device combination
	for _, model := range testModels {
		for _, device := range devices {
			modelName := model.name
			deviceName := device.name

			t.Logf("\n===== Benchmarking %s on %s =====", modelName, deviceName)

			// Completely reset the model between tests
			ResetModel()

			// Initialize model
			if !initBERTModel(t, model.modelID, device.useCPU) {
				t.Logf("Failed to initialize %s on %s, skipping", modelName, deviceName)
				continue
			}

			// Warm up with repeated calls
			for i := 0; i < 3; i++ {
				CalculateSimilarityDefault("Warm up", "Warm up")
			}

			// Measure first inference time
			initStart := time.Now()
			CalculateSimilarityDefault("First inference", "First inference")
			initDuration := time.Since(initStart)
			results[modelName][deviceName]["init_ms"] = float64(initDuration.Milliseconds())

			// Measure classification accuracy
			totalLatency := time.Duration(0)

			// Categorize results by semantic class
			categoryLatency := make(map[string]time.Duration)

			for i, test := range testDataset {
				start := time.Now()
				score := CalculateSimilarityDefault(test.text1, test.text2)
				latency := time.Since(start)
				totalLatency += latency

				categoryLatency[test.class] += latency

				t.Logf("Test %d: %s - Score: %.4f, Latency: %.2f ms",
					i, test.class, score, float64(latency.Milliseconds()))
			}
			// Clean up between models
			ResetModel()
		}
	}

	// Print summary table
	t.Log("\n====== BENCHMARK RESULTS ======")
	t.Log("Model\tSize\tDevice\tInit Time (ms)")

	for _, model := range testModels {
		for _, device := range devices {
			if metrics, ok := results[model.name][device.name]; ok && len(metrics) > 0 {
				initTime := results[model.name][device.name]["init_ms"]

				t.Logf("%s\t%s\t%s\t%.2f",
					model.name, model.size, device.name, initTime)
			}
		}
	}

	// Print comparative analysis tables
	printComparativeTables(t, results, devices, testModels)
}

// printComparativeTables prints detailed comparison tables for model performance
func printComparativeTables(t *testing.T, results map[string]map[string]map[string]float64,
	devices []struct {
		name   string
		useCPU bool
	}, models []struct{ name, modelID, size string }) {

	// Check if we have enough data to make comparisons
	if len(results) == 0 {
		t.Log("\nNot enough data for comparative analysis")
		return
	}
}

// checkGPUAvailability performs a more careful check for GPU availability
func checkGPUAvailability(t *testing.T) bool {
	// Force cleanup first
	ResetModel()

	// Try to initialize a small model with GPU
	t.Log("initialize model on GPU...")
	modelID := "sentence-transformers/all-MiniLM-L6-v2"

	err := InitModel(modelID, false) // false = use GPU
	if err != nil {
		t.Logf("GPU initialization failed: %v", err)
		return false
	}

	// Verify initialization succeeded
	if !IsModelInitialized() {
		t.Log("GPU not available: model initialization failed")
		return false
	}

	// Try a simple inference to confirm
	score := CalculateSimilarityDefault("test", "test")
	if score < 0 {
		t.Log("GPU not available: inference failed")
		return false
	}

	// Clean up after test
	ResetModel()

	t.Log("GPU is available and working")
	return true
}

func TestClassifyText(t *testing.T) {
	// Number of classes for our test classifier (e.g., sentiment: negative, neutral, positive)
	const NUM_CLASSES = 3

	// Initialize the classifier model.
	// Note this model is not fined tuned for sentiment analysis. The actual results may not be as expected.
	err := InitClassifier("sentence-transformers/all-MiniLM-L12-v2", NUM_CLASSES, true) // Use CPU for testing
	if err != nil {
		t.Fatalf("Failed to initialize BERT classifier: %v", err)
	}

	testCases := []struct {
		name string
		text string
	}{
		{
			name: "Positive sentiment",
			text: "I love this product, it's amazing!",
		},
		{
			name: "Negative sentiment",
			text: "This is the worst experience I've ever had.",
		},
		{
			name: "Neutral statement",
			text: "The weather today is partly cloudy with a chance of rain.",
		},
		{
			name: "Technical description",
			text: "This model uses transformer architecture with self-attention mechanism.",
		},
		{
			name: "Question",
			text: "How does the classification system determine sentiment?",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := ClassifyText(tc.text)
			if err != nil {
				t.Fatalf("Classification failed: %v", err)
			}

			// Check that the class index is within the expected range
			// But ignore the result as the model is not fine tuned for sentiment analysis
			if result.Class < 0 || result.Class >= NUM_CLASSES {
				t.Errorf("Expected class index between 0 and %d, got %d", NUM_CLASSES-1, result.Class)
			}

			// Check that the confidence score is between 0 and 1
			if result.Confidence < 0.0 || result.Confidence > 1.0 {
				t.Errorf("Expected confidence score between 0.0 and 1.0, got %f", result.Confidence)
			}

			t.Logf("Text: '%s', Classified as: %d, Confidence: %.4f", tc.text, result.Class, result.Confidence)
		})
	}

	// Test consistency for the same text
	t.Run("Consistency", func(t *testing.T) {
		text := "This is a test sentence for classification."

		result1, err := ClassifyText(text)
		if err != nil {
			t.Fatalf("First classification failed: %v", err)
		}

		result2, err := ClassifyText(text)
		if err != nil {
			t.Fatalf("Second classification failed: %v", err)
		}

		// Same text should be classified into the same class
		if result1.Class != result2.Class {
			t.Errorf("Expected same class for identical text, got %d and %d", result1.Class, result2.Class)
		}

		// Confidence scores should be very close
		confidenceDiff := math.Abs(float64(result1.Confidence - result2.Confidence))
		if confidenceDiff > 1e-4 {
			t.Errorf("Expected similar confidence scores, got %f and %f (diff: %f)",
				result1.Confidence, result2.Confidence, confidenceDiff)
		}
	})
}
