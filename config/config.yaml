bert_model:
  model_id: sentence-transformers/all-MiniLM-L12-v2
  threshold: 0.6
  use_cpu: true
semantic_cache:
  enabled: true
  similarity_threshold: 0.8
  max_entries: 1000
  ttl_seconds: 3600
prompt_guard:
  enabled: true
  model_id: "prompt_guard_fine_tuning/jailbreak_classifier_linear_model"
  threshold: 0.7
  use_cpu: true
  jailbreak_mapping_path: "config/jailbreak_type_mapping.json"
gpu_config:
  flops: 312000000000000  # 312e12 fp16
  hbm: 2000000000000      # 2e12 (2 TB/s)
  description: "A100-80G" # https://www.nvidia.com/content/dam/en-zz/Solutions/Data-Center/a100/pdf/nvidia-a100-datasheet-us-nvidia-1758950-r4-web.pdf
model_config:
  phi4:
    param_count: 14000000000  # 14B parameters https://huggingface.co/microsoft/phi-4
    batch_size: 512.0  # vLLM default batch size
    context_size: 16384.0 # based on https://huggingface.co/microsoft/phi-4
    pii_policy:
      allow_by_default: false  # Deny all PII by default
      pii_types_allowed: ["EMAIL_ADDRESS", "PHONE_NUMBER"]  # Only allow these specific PII types
  gemma3:27b:
    param_count: 27000000000  # 27B parameters (base version)
    batch_size: 512.0
    context_size: 16384.0
    pii_policy:
      allow_by_default: false  # Deny all PII by default
      pii_types_allowed: ["EMAIL_ADDRESS", "PHONE_NUMBER"]  # Only allow these specific PII types
  "mistral-small3.1":
    param_count: 22000000000
    batch_size: 512.0
    context_size: 16384.0
    pii_policy:
      allow_by_default: false  # Deny all PII by default
      pii_types_allowed: ["EMAIL_ADDRESS", "PHONE_NUMBER"]  # Only allow these specific PII types
# Classifier configuration for text classification
classifier:
  category_model:
    model_id: "classifier_model_fine_tuning/category_classifier_linear_model" #TODO: Use local model for now before the code can download the entire model from huggingface
    threshold: 0.6
    use_cpu: true
    category_mapping_path: "config/category_mapping.json"
  pii_model:
    model_id: "pii_model_fine_tuning/pii_classifier_linear_model" #TODO: Use local model for now before the code can download the entire model from huggingface
    threshold: 0.7
    use_cpu: true
    pii_mapping_path: "config/pii_type_mapping.json"
  load_aware: false
categories:
- name: business
  model_scores:
  - model: phi4
    score: 0.8
  - model: gemma3:27b
    score: 0.4
  - model: mistral-small3.1
    score: 0.2
- name: law
  model_scores:
  - model: gemma3:27b
    score: 0.8
  - model: phi4
    score: 0.6
  - model: mistral-small3.1
    score: 0.4
- name: psychology
  model_scores:
  - model: mistral-small3.1
    score: 0.6
  - model: gemma3:27b
    score: 0.4
  - model: phi4
    score: 0.4
- name: biology
  model_scores:
  - model: mistral-small3.1
    score: 0.8
  - model: gemma3:27b
    score: 0.6
  - model: phi4
    score: 0.2
- name: chemistry
  model_scores:
  - model: mistral-small3.1
    score: 0.8
  - model: gemma3:27b
    score: 0.6
  - model: phi4
    score: 0.6
- name: history
  model_scores:
  - model: mistral-small3.1
    score: 0.8
  - model: phi4
    score: 0.6
  - model: gemma3:27b
    score: 0.4
- name: other
  model_scores:
  - model: gemma3:27b
    score: 0.8
  - model: phi4
    score: 0.6
  - model: mistral-small3.1
    score: 0.6
- name: health
  model_scores:
  - model: gemma3:27b
    score: 0.8
  - model: phi4
    score: 0.8
  - model: mistral-small3.1
    score: 0.6
- name: economics
  model_scores:
  - model: gemma3:27b
    score: 0.8
  - model: mistral-small3.1
    score: 0.8
  - model: phi4
    score: 0.0
- name: math
  model_scores:
  - model: phi4
    score: 1.0
  - model: mistral-small3.1
    score: 0.8
  - model: gemma3:27b
    score: 0.6
- name: physics
  model_scores:
  - model: gemma3:27b
    score: 0.4
  - model: phi4
    score: 0.4
  - model: mistral-small3.1
    score: 0.4
- name: computer science
  model_scores:
  - model: gemma3:27b
    score: 0.6
  - model: mistral-small3.1
    score: 0.6
  - model: phi4
    score: 0.0
- name: philosophy
  model_scores:
  - model: phi4
    score: 0.6
  - model: gemma3:27b
    score: 0.2
  - model: mistral-small3.1
    score: 0.2
- name: engineering
  model_scores:
  - model: gemma3:27b
    score: 0.6
  - model: mistral-small3.1
    score: 0.6
  - model: phi4
    score: 0.2
default_model: mistral-small3.1
