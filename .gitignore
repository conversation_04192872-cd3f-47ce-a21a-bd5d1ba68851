# Rust
target/
**/*.rs.bk
Cargo.lock

# Python
*.pyc
*.pyo
*.pyd
*.pyw
*.pyz
__pycache__/
.venv/

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
extproc-server

# IDE
.idea/
.vscode/
*.swp
*.swo

# Dependency directories
vendor/

# Environment variables
.env

# OS specific
.DS_Store
Thumbs.db

# Project specific
bin/

# Model files (too large for git)
*.pt
*.pth
*.bin
*.onnx
*.h5
*/trained_model/*.pt
*/trained_model/*.pth
*/trained_model/*.bin
*/trained_model/*.onnx
*/trained_model/*.h5
*/trained_model/*.json
*/trained_model/*.txt
*/models/*.pt
*/models/*.pth
*/models/*.bin
*/models/*.onnx
*/models/*.h5
*/models/*.json
*/models/*.txt
# Allow README files in model directories
!*/trained_model/README.md
!*/models/README.md

# Added by <PERSON> Task Master
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
node_modules/
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# Task files
tasks.json
tasks/ 
.cursor/
.roo/
.env.example
.taskmasterconfig
example_prd.txt
.roomodes
.windsurfrules
scripts/prd.txt
.env.taskmaster
package-lock.json
package.json
